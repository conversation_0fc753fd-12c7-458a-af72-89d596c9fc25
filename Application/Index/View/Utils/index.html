<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="icon" href="__PUBLIC__/static/annie/img/fovicon.ico">
    <title>卡密购买 - {$con.webname}</title>
    <meta name="google-site-verification" content="FINDSr7GU2MfCZ9NVBBLPrgGHDXfffBCbywFI5YZWOs">
    <meta name="description" content="卡密购买 - {$con.webname}">
    <meta name="keywords" content="卡密购买,会员,{$con.webname}">
    <meta name="author" content="{$con.webname}">
    <script defer="defer" src="__PUBLIC__/static/annie/js/annie.js"></script>
    <link href="__PUBLIC__/static/annie/css/annie.css" rel="stylesheet">
    <link href="__PUBLIC__/static/annie/css/app.css" rel="stylesheet">
    <link href="__PUBLIC__/static/annie/css/annieMod.css" rel="stylesheet">
    <link href="__PUBLIC__/static/annie/css/utils.css" rel="stylesheet">
    <script src="__PUBLIC__/js/qrcode.js"></script>
    <style>
        .ours-select {
            position: relative
        }

        .ours-select .selection-item {
            display: block;
            cursor: pointer;
            width: 100%;
            height: 36px;
            line-height: 36px;
            box-sizing: border-box;
            padding: 0 32px 0 10px;
            position: relative
        }

        /* PC端优化样式 */
        @media (min-width: 768px) {
            .YF28[data-v-7cd14dad] {
                max-width: 1200px;
                margin: 0 auto;
                padding: 0 20px;
            }

            .YF28-header[data-v-d0b9b47a] {
                margin-top: 20px;
                padding: 30px;
                margin-bottom: 20px;
                border-radius: 20px;
            }

            .YF28-header>.YF28-header-main>.text>p[data-v-d0b9b47a]:first-child {
                font-size: 28px;
                margin: 10px 0;
            }

            .YF28-header>.YF28-header-main>.text>p[data-v-d0b9b47a]:nth-child(2) {
                font-size: 16px;
            }

            .td-1[data-v-d0b9b47a] {
                width: 80px;
                left: 50px;
                top: 20px;
            }

            .td-2[data-v-d0b9b47a] {
                width: 30px;
            }

            .YF28-nav[data-v-d0b9b47a] {
                position: absolute;
                top: 20px;
                right: 20px;
            }

            .YF28-nav a[data-v-d0b9b47a] {
                background: rgba(255,255,255,0.2);
                padding: 8px 16px;
                border-radius: 20px;
                color: white;
                text-decoration: none;
                font-size: 14px;
                transition: all 0.3s;
            }

            .YF28-nav a[data-v-d0b9b47a]:hover {
                background: rgba(255,255,255,0.3);
            }

            .warp[data-v-7cd14dad] {
                height: auto;
                overflow: visible;
                margin-top: 20px;
            }

            .YF28-list[data-v-7cd14dad] {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 20px;
                padding: 20px;
                border-radius: 15px;
                background: rgba(255,255,255,0.8);
                backdrop-filter: blur(10px);
            }

            .item[data-v-7cd14dad] {
                width: 100%;
                padding: 20px;
                border-radius: 15px;
                background: white;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                transition: all 0.3s ease;
                margin-bottom: 0;
                font-size: 14px;
                text-align: center;
            }

            .item[data-v-7cd14dad]:hover {
                transform: translateY(-5px);
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            }

            .item>img[data-v-7cd14dad] {
                width: 60px;
                height: 60px;
                border-radius: 12px;
                margin-bottom: 12px;
                object-fit: cover;
            }

            .new-bac[data-v-7cd14dad], .new-bac2[data-v-7cd14dad] {
                display: none;
            }

            .footer-sj[data-v-7cd14dad] {
                margin-top: 40px;
                font-size: 14px;
                padding-top: 20px;
            }
        }

        /* 大屏幕优化 */
        @media (min-width: 1200px) {
            .YF28-list[data-v-7cd14dad] {
                grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
                gap: 25px;
                padding: 30px;
            }

            .item[data-v-7cd14dad] {
                padding: 25px;
            }

            .item>img[data-v-7cd14dad] {
                width: 70px;
                height: 70px;
            }
        }

        /* 平板优化 */
        @media (min-width: 768px) and (max-width: 1199px) {
            .YF28-list[data-v-7cd14dad] {
                grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
                gap: 18px;
            }
        }

        /* 小屏PC优化 */
        @media (min-width: 768px) and (max-width: 991px) {
            .YF28[data-v-7cd14dad] {
                max-width: 100%;
                padding: 0 15px;
            }
        }

        .ours-select .selection-item .arrow-svg {
            position: absolute;
            display: inline-block;
            width: 14px;
            height: 14px;
            top: 50%;
            transform: translateY(-50%);
            right: 10px
        }

        .ours-select .selection-item .arrow-svg path {
            transition: all .3s
        }

        .ours-select .selection-item .arrow-fold::before {
            right: 50%;
            top: 75%;
            transform: rotate(45deg)
        }

        .ours-select .selection-item .arrow-fold::after {
            left: 50%;
            top: 75%;
            transform: rotate(-45deg)
        }

        .ours-select .selection-item .arrow-unfold::before {
            right: 50%;
            top: 25%;
            transform: rotate(-45deg)
        }

        .ours-select .selection-item .arrow-unfold::after {
            left: 50%;
            top: 25%;
            transform: rotate(45deg)
        }

        .ours-select .selector {
            top: 0;
            position: absolute;
            border-radius: 6px;
            overflow: hidden;
            width: 100%;
            background: #f7f7f7
        }

        .ours-select .selector:focus {
            outline: none
        }

        .ours-select .selector.z-index {
            z-index: 9999999999999;
            box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, .14)
        }

        .ours-select .selector .select-dropdown {
            transition: all .2s
        }

        .ours-select .selector .select-dropdown.unfold {
            max-height: 252px;
            overflow-y: auto;
            z-index: 9999999
        }

        .ours-select .selector .select-dropdown.fold {
            max-height: 0px;
            overflow-y: hidden
        }

        .ours-select .option-wrapper {
            cursor: pointer;
            width: 100%;
            height: 36px;
            line-height: 36px;
            box-sizing: border-box;
            padding: 0 32px 0 10px
        }

        .ours-select .option-wrapper:hover {
            background: #fff
        }

        .ours-select .seleted {
            background: #e6f4ff
        }

        .ours-select .option {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            user-select: none
        }
    </style>
    <style>
        .hsq-image__error,
        .hsq-image__inner,
        .hsq-image__placeholder,
        .hsq-image__wrapper {
            height: 100%;
            width: 100%
        }

        .hsq-image {
            display: inline-block;
            overflow: hidden;
            position: relative
        }

        .hsq-image__inner {
            opacity: 1;
            vertical-align: top
        }

        .hsq-image__inner.is-loading {
            opacity: 0
        }

        .hsq-image__wrapper {
            left: 0;
            position: absolute;
            top: 0
        }

        .hsq-image__error,
        .hsq-image__placeholder {
            background: var(--hsq-fill-color-light)
        }

        .hsq-image__error {
            align-items: center;
            color: var(--hsq-text-color-placeholder);
            display: flex;
            font-size: 14px;
            justify-content: center;
            vertical-align: middle
        }

        .hsq-image__preview {
            cursor: pointer
        }

        .hsq-image-viewer__wrapper {
            bottom: 0;
            left: 0;
            position: fixed;
            right: 0;
            top: 0
        }

        .hsq-image-viewer__btn {
            align-items: center;
            border-radius: 50%;
            box-sizing: border-box;
            cursor: pointer;
            display: flex;
            justify-content: center;
            opacity: .8;
            position: absolute;
            -webkit-user-select: none;
            user-select: none;
            z-index: 1
        }

        .hsq-image-viewer__btn .hsq-icon {
            cursor: pointer;
            font-size: inherit
        }

        .hsq-image-viewer__close {
            font-size: 40px;
            height: 40px;
            right: 40px;
            top: 40px;
            width: 40px
        }

        .hsq-image-viewer__canvas {
            align-items: center;
            display: flex;
            height: 100%;
            justify-content: center;
            position: static;
            -webkit-user-select: none;
            user-select: none;
            width: 100%
        }

        .hsq-image-viewer__actions {
            background-color: var(--hsq-text-color-regular);
            border-color: #fff;
            border-radius: 22px;
            bottom: 30px;
            height: 44px;
            left: 50%;
            padding: 0 23px;
            transform: translate(-50%);
            width: 282px
        }

        .hsq-image-viewer__actions__inner {
            align-items: center;
            color: #fff;
            cursor: default;
            display: flex;
            font-size: 23px;
            height: 100%;
            justify-content: space-around;
            width: 100%
        }

        .hsq-image-viewer__prev {
            left: 40px
        }

        .hsq-image-viewer__next,
        .hsq-image-viewer__prev {
            background-color: var(--hsq-text-color-regular);
            border-color: #fff;
            color: #fff;
            font-size: 24px;
            height: 44px;
            top: 50%;
            transform: translateY(-50%);
            width: 44px
        }

        .hsq-image-viewer__next {
            right: 40px;
            text-indent: 2px
        }

        .hsq-image-viewer__close {
            background-color: var(--hsq-text-color-regular);
            border-color: #fff;
            color: #fff;
            font-size: 24px;
            height: 44px;
            width: 44px
        }

        .hsq-image-viewer__mask {
            background: #000;
            height: 100%;
            left: 0;
            opacity: .5;
            position: absolute;
            top: 0;
            width: 100%
        }

        .viewer-fade-enter-active {
            animation: viewer-fade-in var(--hsq-transition-duration)
        }

        .viewer-fade-leave-active {
            animation: viewer-fade-out var(--hsq-transition-duration)
        }

        @keyframes viewer-fade-in {
            0% {
                opacity: 0;
                transform: translate3d(0, -20px, 0)
            }

            to {
                opacity: 1;
                transform: translateZ(0)
            }
        }

        @keyframes viewer-fade-out {
            0% {
                opacity: 1;
                transform: translateZ(0)
            }

            to {
                opacity: 0;
                transform: translate3d(0, -20px, 0)
            }
        }

        .hsq-popper.is-gj-update-tips {
            background: #ff9800;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0, 23, 54, .1);
            color: #fff;
            padding: 20px 32px 17px
        }

        .hsq-popper.is-gj-update-tips .hsq-popper__arrow:before {
            background: #ff9800
        }

        .gj-tooltips-info[data-v-ec77157e] {
            align-items: flex-end;
            display: flex;
            flex-direction: column;
            max-width: 400px
        }

        .gj-tooltips-info .gj-info[data-v-ec77157e] {
            font-size: 15px;
            text-align: left;
            width: 100%
        }

        .gj-tooltips-info .gj-info .gj-time[data-v-ec77157e] {
            align-items: center;
            display: flex;
            font-size: 16px;
            font-weight: 700;
            justify-content: flex-start;
            margin: 8px 0
        }

        .gj-tooltips-info .gj-info .gj-time .hsq-icon[data-v-ec77157e] {
            font-size: 20px;
            margin-right: 10px
        }

        .gj-b-g[data-v-ec77157e] {
            align-items: center;
            display: flex;
            font-size: 12px;
            justify-content: flex-end;
            margin-top: 24px;
            transform: scale(.95);
            transform-origin: right
        }

        .gj-b-g .gj-set-hide[data-v-ec77157e] {
            color: #fff;
            cursor: pointer;
            margin-right: 10px
        }

        /* 卡密购买页面样式 */
        .product-item {
            display: flex !important;
            flex-direction: column;
            align-items: center;
            padding: 30px 20px !important;
            border: 2px solid #e0e0e0;
            border-radius: 15px !important;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        }

        .product-item:hover {
            border-color: #007bff;
            box-shadow: 0 8px 25px rgba(0,123,255,0.15) !important;
            transform: translateY(-8px) !important;
        }

        .product-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .product-info {
            text-align: center;
            margin-bottom: 20px;
        }

        .product-info h3 {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }

        .product-desc {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .product-price {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin: 0;
        }

        .buy-btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
        }

        .buy-btn:hover {
            background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,123,255,0.4);
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: #fff;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            position: relative;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            position: absolute;
            right: 15px;
            top: 15px;
        }

        .close:hover {
            color: #000;
        }

        .modal h2 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .payment-options {
            display: flex;
            gap: 15px;
            margin: 20px 0;
        }

        .payment-option {
            flex: 1;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .payment-option:hover {
            border-color: #007bff;
            background-color: #f8f9ff;
        }

        .payment-option.selected {
            border-color: #007bff;
            background-color: #e3f2fd;
        }

        .payment-option img {
            width: 40px;
            height: 40px;
            margin-bottom: 8px;
        }

        .qr-container {
            text-align: center;
            margin: 20px 0;
        }

        .qr-container canvas {
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        .modal-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .modal-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .modal-btn.primary {
            background-color: #007bff;
            color: white;
        }

        .modal-btn.primary:hover {
            background-color: #0056b3;
        }

        .modal-btn.secondary {
            background-color: #6c757d;
            color: white;
        }

        .modal-btn.secondary:hover {
            background-color: #545b62;
        }

        .loading {
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .success-message, .error-message {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: center;
        }

        .success-message {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .token-display {
            background: linear-gradient(135deg, #e3f2fd 0%, #f8f9ff 100%);
            padding: 20px;
            border-radius: 12px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            border: 3px dashed #007bff;
            color: #0056b3;
            letter-spacing: 2px;
            box-shadow: 0 4px 15px rgba(0,123,255,0.1);
            position: relative;
            overflow: hidden;
        }

        .token-display::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* 复制按钮样式 */
        .copy-token-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40,167,69,0.3);
            margin-top: 10px;
        }

        .copy-token-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40,167,69,0.4);
        }

        .copy-token-btn:active {
            transform: translateY(0);
        }

        /* 卡密标题样式 */
        .token-title {
            color: #007bff;
            margin-bottom: 10px;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .token-subtitle {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        /* PC端商品间距优化 */
        @media (min-width: 768px) {
            .YF28-list[data-v-7cd14dad] {
                gap: 40px !important;
                padding: 40px !important;
            }

            .product-item {
                margin-bottom: 0 !important;
            }
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .modal-content {
                margin: 10% auto;
                width: 95%;
                padding: 20px;
            }

            .payment-options {
                flex-direction: column;
            }

            .product-item {
                margin-bottom: 30px !important;
            }

            .product-icon {
                font-size: 36px;
            }

            .product-info h3 {
                font-size: 18px;
            }

            .product-price {
                font-size: 20px;
            }

            .buy-btn {
                padding: 10px 25px;
                font-size: 14px;
            }

            .qr-container canvas {
                max-width: 250px;
                height: auto;
            }

            .token-display {
                font-size: 16px;
                word-break: break-all;
            }

            .YF28-list[data-v-7cd14dad] {
                gap: 25px !important;
                padding: 25px !important;
            }
        }

        .gj-icon-for-open[data-v-f706fe10] * {
            box-sizing: content-box
        }

        .gj-icon-for-open[data-v-f706fe10] {
            align-items: center;
            background-color: #fff5e6;
            border: 1px solid #ffcc80;
            border-bottom-right-radius: 50px;
            border-left: 0;
            border-top-right-radius: 50px;
            box-shadow: 0 0 9px 4px #ffeacc !important;
            box-sizing: content-box;
            color: #ff9800;
            cursor: pointer;
            display: flex;
            flex-direction: row;
            height: 28px;
            justify-content: center;
            padding-right: 10px;
            position: fixed;
            -webkit-user-select: none;
            z-index: 2147483647
        }

        .gj-icon-for-open>.hsq-icon[data-v-f706fe10] {
            font-size: 14px
        }

        .gj-icon[data-v-f706fe10] {
            border: 0;
            height: 18px;
            margin-left: 6px;
            padding: 0;
            pointer-events: none;
            width: 18px
        }

        .gt-text[data-v-f706fe10] {
            color: #ff9800;
            font-size: 12px;
            padding: 5px 0 5px 5px
        }

        .openGjGlobalTool[data-v-f706fe10] {
            color: #ff9800;
            padding: 5px 0
        }

        .gt-text[data-v-f706fe10],
        .openGjGlobalTool[data-v-f706fe10] {
            pointer-events: none
        }
    </style>

</head>

<body inmaintabuse="1">
    <div id="app" data-v-app="">
        <div data-v-7cd14dad="" class="YF28">
            <div data-v-d0b9b47a="" data-v-7cd14dad="" class="YF28-header">
                <div data-v-d0b9b47a="" class="YF28-header-main"><img data-v-d0b9b47a=""
                        src="data:image/png;base64,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"
                        alt="" class="td-1"><img data-v-d0b9b47a=""
                        src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADEAAAAoCAYAAABXRRJPAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAADYUlEQVRYR9WZaU8UQRCGuRU0igkigigQRUICwagREI3igSZEEIwcghwCP1N/Gj5vUoY5ao6ePVjf5Pmy01X1znZPbfdsW606Pz/vg/swB4vwHj4aH+AVPIUJ6Id2C708yQQMgwzvwFkgP2EZdOOWtUmiYA/MwjZ45qpwAA+hsbNDgU6QeX2DnpF6cAJ3rWR9ReJB+G6FmoGWZ6+Vr00kEs/gFLxijWYBqi8xgrtAncVL3kx2oc9slRdBWv/rlqQVOIQhs1csBqt1rlpwK3EMw2YzXwx8EwlsNTQj/WbVFwPUhbzgEPZhBfRQ6td7Bp6DvpwNqLVJbEGPWY6LC1pG6tNeYBH6sZqHAUuXKcZcgUn4Al6uMry1dHFxQSa8gDx+gfZD3ZYmSMRp2/IVvNxFjFmaC/Ghvp29yKAiNiF/fZYQObQCtOxCl5lab6eluRAfdsMLKFpWn6DLwuoi8mlW9OB69bKYtfC0uHgD9HB6gZ+hw4bWVeRVY1Er9ep6aB+Xno2oGHAPonum7M4Q0fbvP+3QD2PGKFyzy7ki/3ikXhkmLTRbDOoAtUl1oEH72JWMwgLsw5nDJkxB7kxSZwk8wx7rFlYsBuc+A2buGDzzSXQzmU2BWno2Q7b84XurpDA0FzFYlkO4bSlSwpjOLp5hj2kLqyaMaM17JsugZeeeGzCm2TiKGM3jnYWFCwNdsGeGqrJs6VKSuYTZLHYtJFwYmE4Yqsp1SxkT5h4lzOZRadegm1hNmKnKE0sZE8ZuJozmccvCwkTxst2oiBVLGRPG1N7LbkdGLay8KCw8Q1XYsrQpYU6/T57pJOkNYZEo3J0wUgvfLG1KmNNGzzOdZNxCwkTxWjvTP9YsZUqYK7uXCp8JieIrCTNVWbKUMWFMByjPsEf5FwlRUXwiYaYqI5YyJoxpe+4Z9qh2tqF4B+xEzFRhAyxjXBjTizvPcBKdf6ofDzAwAqdmKJQTyNwZY6zsq9PMxlBaGKm6f5qyFClhbChhNA/3mQoWhq5C2aV1BBMW6gpjIS/wHlhYfYS5ITPpmT+Al1C4/8fYKJRZTmrBDTku62Z0PJ2EGXgMdyComMyBzhV5LxAWbXhrC6O98DpiPEq13etlCcMDsBa5gXm79H8J40L/7/2AxjwLzVL6Btra/gIPMo2eXmEMCwAAAABJRU5ErkJggg=="
                        alt="" class="td-2">
                    <div data-v-d0b9b47a="" class="text">
                        <p data-v-d0b9b47a="">{$con.webname}·卡密购买</p>
                        <p data-v-d0b9b47a="">选择您需要的会员套餐</p>
                    </div>
                </div>
                <div data-v-d0b9b47a="" class="YF28-nav"><a
                        data-v-d0b9b47a="" href="/">返回首页</a></div>
            </div>
            <div data-v-7cd14dad="" class="list-box">
                <div data-v-7cd14dad="" class="warp">
                    <div data-v-7cd14dad="" class="YF28-list" id="product-list">
                        <!-- 商品85: 月度卡密 -->
                        <div data-v-7cd14dad="" class="item product-item" data-product-id="85" data-lx="1">
                            <div class="product-icon">💎</div>
                            <div class="product-info">
                                <h3>月度卡密</h3>
                                <p class="product-desc">买一月 送1周，共38天</p>
                                <p class="product-price">¥28.05</p>
                            </div>
                            <button class="buy-btn" onclick="selectProduct(85, 1, '月度卡密', '28.05')">立即购买</button>
                        </div>

                        <!-- 商品86: 永久卡密 -->
                        <div data-v-7cd14dad="" class="item product-item" data-product-id="86" data-lx="2">
                            <div class="product-icon">👑</div>
                            <div class="product-info">
                                <h3>永久卡密</h3>
                                <p class="product-desc">加10元，得永久，终身使用，永不到期</p>
                                <p class="product-price">¥38.05</p>
                            </div>
                            <button class="buy-btn" onclick="selectProduct(86, 2, '永久卡密', '38.05')">立即购买</button>
                        </div>
                    </div>
                </div>
            </div>
            <div data-v-4ad322e2="" data-v-7cd14dad="" class="footer-sj" style="margin-top: 20vw;">
         <span data-v-4ad322e2="">{$con.gonggao}<sup
                        data-v-4ad322e2="">®</sup></span>
            </div>
        </div>
    </div>

    <!-- 支付模态框 -->
    <div id="paymentModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2 id="modalTitle">选择支付方式</h2>

            <!-- 支付方式选择 -->
            <div id="paymentSelection" class="payment-options">
                <div class="payment-option" data-pay-type="wxpay" onclick="selectPayment('wxpay')">
                    <div style="font-size: 40px;">💚</div>
                    <div>微信支付</div>
                </div>
                <div class="payment-option" data-pay-type="alipay" onclick="selectPayment('alipay')">
                    <div style="font-size: 40px;">💙</div>
                    <div>支付宝</div>
                </div>
            </div>

            <!-- 加载状态 -->
            <div id="loadingState" class="loading" style="display: none;">
                <div class="spinner"></div>
                <p>正在生成支付二维码...</p>
            </div>

            <!-- 二维码显示 -->
            <div id="qrCodeState" style="display: none;">
                <div class="qr-container">
                    <div id="qrcode"></div>
                    <p style="margin-top: 15px; color: #666;">请使用手机扫码支付</p>
                    <p id="orderInfo" style="font-size: 14px; color: #999; margin-top: 10px;"></p>
                    <div id="paymentTip" style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 10px; margin: 15px 0; font-size: 13px; color: #856404; display: none;">
                        💡 支付完成后，点击"我已支付"按钮。
                    </div>
                </div>
                <div class="modal-buttons">
                    <button class="modal-btn primary" onclick="checkPaymentStatus()">我已支付</button>
                    <button class="modal-btn secondary" onclick="closeModal()">取消</button>
                </div>
            </div>

            <!-- 支付检查状态 -->
            <div id="checkingState" class="loading" style="display: none;">
                <div class="spinner"></div>
                <p>正在检查支付状态...</p>
            </div>

            <!-- 成功状态 -->
            <div id="successState" style="display: none;">
                <div class="success-message">
                    <h3>🎉 注册成功！</h3>
                    <p>您的卡密已生成，请妥善保存</p>
                </div>
                <div id="orderDetails"></div>

                <!-- 卡密展示区域 -->
                <div style="text-align: center; margin: 20px 0;">
                    <h4 style="color: #007bff; margin-bottom: 10px; font-size: 18px;">
                        🔑 您的专属卡密
                    </h4>
                    <p style="color: #666; font-size: 14px; margin-bottom: 15px;">
                        请复制下方卡密到软件中使用
                    </p>
                    <div class="token-display" id="tokenDisplay"></div>
                    <button class="btn" onclick="copyToken()" style="margin-top: 10px; padding: 8px 20px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        📋 复制卡密
                    </button>
                </div>

                <p style="color: #dc3545; font-weight: bold; text-align: center; margin-top: 15px;">
                    ⚠️ 请谨慎保存卡密，丢失后无法找回（建议截图保存）
                </p>
                <div class="modal-buttons">
                    <button class="modal-btn primary" onclick="closeModal()">确定</button>
                </div>
            </div>

            <!-- 失败状态 -->
            <div id="errorState" style="display: none;">
                <div class="error-message">
                    <h3>❌ 操作失败</h3>
                    <p id="errorMessage"></p>
                </div>
                <div id="errorOrderDetails"></div>
                <p style="text-align: center; margin-top: 15px;">
                    请联系客服处理：<a href="https://t.me/Dataso" target="_blank" style="color: #007bff;">https://t.me/Dataso</a>
                </p>
                <div class="modal-buttons">
                    <button class="modal-btn primary" onclick="closeModal()">确定</button>
                </div>
            </div>
        </div>
    </div>
    <script>
        // 全局变量
        let currentOrder = null;
        let selectedProduct = null;
        let selectedPayType = null;

        // 选择商品
        function selectProduct(productId, lx, productName, price) {
            selectedProduct = {
                id: productId,
                lx: lx,
                name: productName,
                price: price
            };

            document.getElementById('modalTitle').textContent = `购买 ${productName} - ¥${price}`;
            showModal();
            showPaymentSelection();
        }

        // 显示模态框
        function showModal() {
            document.getElementById('paymentModal').style.display = 'block';
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('paymentModal').style.display = 'none';
            resetModal();
        }

        // 重置模态框状态
        function resetModal() {
            hideAllStates();
            selectedPayType = null;
            currentOrder = null;
            document.querySelectorAll('.payment-option').forEach(option => {
                option.classList.remove('selected');
            });
        }

        // 隐藏所有状态
        function hideAllStates() {
            document.getElementById('paymentSelection').style.display = 'none';
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('qrCodeState').style.display = 'none';
            document.getElementById('checkingState').style.display = 'none';
            document.getElementById('successState').style.display = 'none';
            document.getElementById('errorState').style.display = 'none';
        }

        // 显示支付方式选择
        function showPaymentSelection() {
            hideAllStates();
            document.getElementById('paymentSelection').style.display = 'flex';
        }

        // 选择支付方式
        function selectPayment(payType) {
            selectedPayType = payType;

            // 更新UI
            document.querySelectorAll('.payment-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.querySelector(`[data-pay-type="${payType}"]`).classList.add('selected');

            // 延迟一下再创建订单，让用户看到选择效果
            setTimeout(() => {
                createOrder();
            }, 500);
        }

        // 创建订单
        async function createOrder() {
            if (!selectedProduct || !selectedPayType) {
                alert('请选择商品和支付方式');
                return;
            }

            showLoading();

            try {
                const response = await fetch(`https://cloudshop.qnm6.top/create_order.php?customer_contact=customer_${Date.now()}@temp.com&product_id=${selectedProduct.id}&pay_type=${selectedPayType}`);
                const data = await response.json();

                if (data.status === 'success') {
                    currentOrder = data.data;
                    generateQRCode(data.data.payment_url);
                    showQRCode();
                } else {
                    showError('创建订单失败：' + (data.message || '未知错误'));
                }
            } catch (error) {
                console.error('创建订单错误:', error);
                showError('网络错误，请重试');
            }
        }

        // 显示加载状态
        function showLoading() {
            hideAllStates();
            document.getElementById('loadingState').style.display = 'block';
        }

        // 生成二维码
        function generateQRCode(url) {
            const qrcodeDiv = document.getElementById('qrcode');
            qrcodeDiv.innerHTML = '';

            try {
                const qr = qrcode(0, 'M');
                qr.addData(url);
                qr.make();

                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const modules = qr.getModuleCount();
                const cellSize = 300 / modules;

                canvas.width = 300;
                canvas.height = 300;

                // 设置背景色为白色
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, 300, 300);

                // 绘制二维码
                ctx.fillStyle = '#000000';
                for (let row = 0; row < modules; row++) {
                    for (let col = 0; col < modules; col++) {
                        if (qr.isDark(row, col)) {
                            ctx.fillRect(col * cellSize, row * cellSize, cellSize, cellSize);
                        }
                    }
                }

                qrcodeDiv.appendChild(canvas);
            } catch (error) {
                console.error('生成二维码错误:', error);
                showError('生成二维码失败');
            }
        }

        // 显示二维码
        function showQRCode() {
            hideAllStates();
            document.getElementById('qrCodeState').style.display = 'block';

            if (currentOrder) {
                document.getElementById('orderInfo').textContent =
                    `订单号: ${currentOrder.order_info.order_id} | 金额: ¥${currentOrder.order_info.product_price}`;
            }
        }

        // 检查支付状态
        async function checkPaymentStatus() {
            if (!currentOrder) {
                alert('订单信息丢失，请重新购买');
                return;
            }

            showChecking();

            try {
                const response = await fetch(`https://cloudshop.qnm6.top/check_payment_status.php?order_id=${currentOrder.order_info.order_id}`);
                const data = await response.json();

                if (data.status === 'success') {
                    if (data.data.order_status === 'paid') {
                        // 支付成功，注册会员Token
                        await registerVipToken();
                    } else {
                        // 未检测到支付，只弹出提示，不关闭二维码
                        alert('⚠️ 未检测到支付\n\n请确认您已完成支付，然后再次点击"我已支付"按钮。\n\n');
                        showQRCode(); // 重新显示二维码界面
                        // 显示支付提示
                        document.getElementById('paymentTip').style.display = 'block';
                    }
                } else {
                    showError('检查支付状态失败：' + (data.message || '未知错误'));
                }
            } catch (error) {
                console.error('检查支付状态错误:', error);
                showError('网络错误，请重试');
            }
        }

        // 显示检查状态
        function showChecking() {
            hideAllStates();
            document.getElementById('checkingState').style.display = 'block';
        }

        // 注册会员Token
        async function registerVipToken() {
            try {
                const response = await fetch(`https://api.qnm6.top/admapi/vipregister.php?lx=${selectedProduct.lx}`);
                const data = await response.json();

                if (data.code === 200 && data.status === 'success') {
                    showSuccess(data);
                } else {
                    showError('注册失败：' + (data.message || '未知错误'), true);
                }
            } catch (error) {
                console.error('注册Token错误:', error);
                showError('注册失败，网络错误', true);
            }
        }

        // 显示成功状态
        function showSuccess(tokenData) {
            hideAllStates();
            document.getElementById('successState').style.display = 'block';

            document.getElementById('orderDetails').innerHTML = `
                <p><strong>订单号:</strong> ${currentOrder.order_info.order_id}</p>
                <p><strong>商品:</strong> ${selectedProduct.name}</p>
                <p><strong>注册时间:</strong> ${tokenData.reg_time}</p>
                <p><strong>到期时间:</strong> ${tokenData.expire_time}</p>
            `;

            document.getElementById('tokenDisplay').textContent = tokenData.token;
        }

        // 复制卡密到剪贴板
        function copyToken() {
            const tokenElement = document.getElementById('tokenDisplay');
            const token = tokenElement.textContent;

            if (navigator.clipboard && window.isSecureContext) {
                // 使用现代API
                navigator.clipboard.writeText(token).then(() => {
                    showCopySuccess();
                }).catch(err => {
                    fallbackCopyTextToClipboard(token);
                });
            } else {
                // 降级方案
                fallbackCopyTextToClipboard(token);
            }
        }

        // 降级复制方案
        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";
            textArea.style.opacity = "0";

            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    showCopySuccess();
                } else {
                    alert('复制失败，请手动复制卡密');
                }
            } catch (err) {
                alert('复制失败，请手动复制卡密');
            }

            document.body.removeChild(textArea);
        }

        // 显示复制成功提示
        function showCopySuccess() {
            const button = event.target;
            const originalText = button.textContent;
            button.textContent = '✅ 已复制';
            button.style.background = '#28a745';

            setTimeout(() => {
                button.textContent = originalText;
                button.style.background = '#28a745';
            }, 2000);
        }

        // 显示错误状态
        function showError(message, isRegisterError = false) {
            hideAllStates();
            document.getElementById('errorState').style.display = 'block';
            document.getElementById('errorMessage').textContent = message;

            if (isRegisterError && currentOrder) {
                document.getElementById('errorOrderDetails').innerHTML = `
                    <p><strong>订单号:</strong> ${currentOrder.order_info.order_id}</p>
                    <p><strong>商品:</strong> ${selectedProduct.name}</p>
                `;
            }
        }

        // 点击模态框外部关闭（仅在特定状态下允许）
        window.onclick = function(event) {
            const modal = document.getElementById('paymentModal');
            if (event.target === modal) {
                // 检查当前状态，只有在支付方式选择状态下才允许点击外部关闭
                const paymentSelection = document.getElementById('paymentSelection');

                const canClose = paymentSelection.style.display !== 'none';

                if (canClose) {
                    closeModal();
                }
            }
        }
    </script>

    <script src="chrome-extension://bcjkohpafljnigdmnlaghjhaaleonlac/sm.bundle.js" data-pname="fatkun-mv3-new"
        data-asset-path="https://fkm3n.s3.ap-northeast-2.amazonaws.com"></script>
    <div id="hsq-popper-container-9519"><!--v-if--></div><span data-v-app="">
        <div data-v-f706fe10="" class="gj-icon-for-open hsq-tooltip__trigger hsq-tooltip__trigger"
            style="left: 0px; top: 84.4px; display: none;">
            <div data-v-f706fe10="" class="gj-icon gjt-flex2-x"><img data-v-adb92754="" data-v-f706fe10=""
                    src="https://s.dxcdn.cn/hsq_8_5/assets/svg/65a791c1.LVc347ie.svg" alt="哈"
                    style="width: 100%; height: 100%; margin: 0px;"></div><span data-v-f706fe10="" class="gt-text"
                style="display: none;">展开工具箱</span><i data-v-f706fe10="" class="hsq-icon" style="display: none;"><svg
                    data-v-f706fe10="" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                    <path fill="currentColor"
                        d="M338.752 104.704a64 64 0 0 0 0 90.496l316.8 316.8-316.8 316.8a64 64 0 0 0 90.496 90.496l362.048-362.048a64 64 0 0 0 0-90.496L429.248 104.704a64 64 0 0 0-90.496 0z">
                    </path>
                </svg></i>
        </div><!---->
    </span>
    <div id="__REC__-container"></div>
</body><fatkun-drop-panel style="display: none;"></fatkun-drop-panel>

</html>