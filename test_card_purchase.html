<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密购买测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .api-test {
            margin: 10px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>🔧 卡密购买系统测试</h1>
    
    <div class="test-section">
        <h2>API 接口测试</h2>
        
        <div class="api-test">
            <h3>1. 创建订单测试</h3>
            <button onclick="testCreateOrder(85, 'wxpay')">测试创建订单 (商品85, 微信支付)</button>
            <button onclick="testCreateOrder(86, 'alipay')">测试创建订单 (商品86, 支付宝)</button>
            <div id="createOrderResult" class="result"></div>
        </div>
        
        <div class="api-test">
            <h3>2. 检查支付状态测试</h3>
            <input type="text" id="orderIdInput" placeholder="输入订单ID" style="padding: 8px; margin: 5px;">
            <button onclick="testCheckPayment()">检查支付状态</button>
            <div id="checkPaymentResult" class="result"></div>
        </div>
        
        <div class="api-test">
            <h3>3. 注册会员Token测试</h3>
            <button onclick="testRegisterToken(1)">注册38天会员</button>
            <button onclick="testRegisterToken(2)">注册永久会员</button>
            <div id="registerTokenResult" class="result"></div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>二维码生成测试</h2>
        <input type="text" id="qrInput" placeholder="输入要生成二维码的内容" style="padding: 8px; width: 300px;">
        <button onclick="testQRCode()">生成二维码</button>
        <div id="qrResult" style="text-align: center; margin: 20px 0;"></div>
    </div>

    <script src="Public/js/qrcode.js"></script>
    <script>
        // 测试创建订单
        async function testCreateOrder(productId, payType) {
            const resultDiv = document.getElementById('createOrderResult');
            resultDiv.textContent = '正在测试创建订单...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch(`https://cloudshop.qnm6.top/create_order.php?customer_contact=test_${Date.now()}@temp.com&product_id=${productId}&pay_type=${payType}`);
                const data = await response.json();
                
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.className = data.status === 'success' ? 'result success' : 'result error';
                
                if (data.status === 'success') {
                    document.getElementById('orderIdInput').value = data.data.order_info.order_id;
                }
            } catch (error) {
                resultDiv.textContent = '错误: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
        
        // 测试检查支付状态
        async function testCheckPayment() {
            const orderId = document.getElementById('orderIdInput').value;
            const resultDiv = document.getElementById('checkPaymentResult');
            
            if (!orderId) {
                resultDiv.textContent = '请输入订单ID';
                resultDiv.className = 'result error';
                return;
            }
            
            resultDiv.textContent = '正在检查支付状态...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch(`https://cloudshop.qnm6.top/check_payment_status.php?order_id=${orderId}`);
                const data = await response.json();
                
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.className = data.status === 'success' ? 'result success' : 'result error';
            } catch (error) {
                resultDiv.textContent = '错误: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
        
        // 测试注册Token
        async function testRegisterToken(lx) {
            const resultDiv = document.getElementById('registerTokenResult');
            resultDiv.textContent = '正在注册会员Token...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch(`https://api.qnm6.top/admapi/vipregister.php?lx=${lx}`);
                const data = await response.json();
                
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.className = data.code === 200 ? 'result success' : 'result error';
            } catch (error) {
                resultDiv.textContent = '错误: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
        
        // 测试二维码生成
        function testQRCode() {
            const input = document.getElementById('qrInput').value;
            const resultDiv = document.getElementById('qrResult');
            
            if (!input) {
                alert('请输入要生成二维码的内容');
                return;
            }
            
            try {
                resultDiv.innerHTML = '';
                
                const qr = qrcode(0, 'M');
                qr.addData(input);
                qr.make();
                
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const modules = qr.getModuleCount();
                const cellSize = 200 / modules;
                
                canvas.width = 200;
                canvas.height = 200;
                
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, 200, 200);
                
                ctx.fillStyle = '#000000';
                for (let row = 0; row < modules; row++) {
                    for (let col = 0; col < modules; col++) {
                        if (qr.isDark(row, col)) {
                            ctx.fillRect(col * cellSize, row * cellSize, cellSize, cellSize);
                        }
                    }
                }
                
                resultDiv.appendChild(canvas);
                
                const p = document.createElement('p');
                p.textContent = '二维码生成成功！';
                p.style.color = '#155724';
                resultDiv.appendChild(p);
                
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: #721c24;">生成二维码失败: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
